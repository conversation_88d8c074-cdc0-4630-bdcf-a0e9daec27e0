import sys
import os
import webview
import multiprocessing
import signal
import win32gui
import win32con
import time
import threading
script=os.path.abspath(__file__)
home=os.path.dirname(script)
os.chdir(home)
sys.path.append(".")
sys.path.append("..")

def on_closing():
    """Handle window close event"""
    print("Closing application...")
    # Terminate the server process
    if 'server_process' in globals() and server_process.is_alive():
        print("Terminating server process...")
        server_process.terminate()
        server_process.join() # Wait for the process to terminate
    print("Application closed.")

def _set_icon(title, icon_path):
    """Set the window icon using win32gui after the window is created."""
    try:
        # Poll for the window to appear, as it might not be ready immediately
        hwnd = 0
        for _ in range(20):  # Try for 10 seconds
            hwnd = win32gui.FindWindow(None, title)
            if hwnd != 0:
                break
            time.sleep(0.5)

        if hwnd == 0:
            print("Error setting window icon: Window not found.")
            return
        # 加载图标文件
        # LR_LOADFROMFILE: 从文件加载
        # LR_DEFAULTSIZE: 使用图标文件中指定的默认大小
        h_icon = win32gui.LoadImage(0, icon_path, win32con.IMAGE_ICON, 0, 0, win32con.LR_LOADFROMFILE | win32con.LR_DEFAULTSIZE)

        # 设置窗口的大图标和小图标
        win32gui.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_BIG, h_icon)
        win32gui.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_SMALL, h_icon)
    except Exception as e:
        print(f"Error setting window icon: {e}")

# This global variable will be assigned in the thread
server_process = None

def load_main_app(window):
    """Starts the server in a background thread and loads the main URL."""
    global server_process
    print("Starting backend server...")
    from src.web.http import start_server
    server_process = start_server()

    # Give the server a moment to start up.
    # A more robust solution would be to ping http://localhost:8000 until it responds.
    time.sleep(4)

    print("Server started. Loading main application...")
    window.load_url("http://localhost:8000")

if __name__ == "__main__":
    multiprocessing.freeze_support()
    
    # Define the path to the loading file
    loading_html_path = os.path.join(home, 'web', 'loading.html')

    # Create window pointing to the loading screen
    window = webview.create_window(
        "信小财",
        url=loading_html_path,
        width=1600,
        height=900,
        confirm_close=True
    )

    # Set the close event handler
    window.events.closed += on_closing

    # Handle Ctrl+C in console
    signal.signal(signal.SIGINT, lambda s, f: on_closing())

    # Start the server and load the main app in a separate thread
    threading.Thread(target=load_main_app, args=(window,)).start()

    import src.base.settings as setting
    ico_path = setting.CACHE_PATH + "/rpa.ico"
    chinese = {
        'global.quitConfirmation': u'确定关闭?',
    }
    try:
        # webview.start's icon parameter is not effective under the webview2 engine, 
        # so we use win32gui to set the icon instead.
        webview.start(localization=chinese, debug=False, http_server=True, func=lambda: _set_icon("信小财", ico_path))
    except KeyboardInterrupt:
        on_closing()