<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>信小财机器人 启动中...</title>
<style>
  :root {
    --primary-color: #00ffff; /* 科技蓝 */
    --secondary-color: #ff00ff; /* 品红 */
    --background-color: #0d0d21; /* 深邃星空黑 */
    --particle-color: #ffffff;
  }

  body, html {
    height: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-color);
    overflow: hidden;
    font-family: 'Orbitron', 'Segoe UI', sans-serif;
  }

  .scene {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 粒子效果 */
  .particle {
    position: absolute;
    background: var(--particle-color);
    border-radius: 50%;
    animation: float 20s infinite linear;
  }

  /* 电路板背景 */
  .grid-background {
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 100%;
    background-image:
      linear-gradient(var(--primary-color, transparent) 1px, transparent 1px),
      linear-gradient(90deg, var(--primary-color, transparent) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.1;
    animation: pan-grid 30s linear infinite;
  }

  .loader-container {
    text-align: center;
    color: var(--primary-color);
    z-index: 10;
  }

  .robot {
    width: 150px;
    height: 150px;
    position: relative;
    animation: hover 4s ease-in-out infinite;
  }

  .robot-head {
    width: 100px;
    height: 80px;
    background: linear-gradient(145deg, #e0e0e0, #bababa);
    border-radius: 50% 50% 10% 10% / 60% 60% 40% 40%;
    position: absolute;
    top: 0;
    left: 25px;
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 15px var(--primary-color) inset;
  }

  .eye-visor {
    width: 80%;
    height: 25px;
    background: #111;
    border: 2px solid var(--primary-color);
    border-radius: 10px;
    position: absolute;
    top: 25px;
    left: 10%;
    overflow: hidden;
  }

  .scan-line {
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    position: absolute;
    top: 0;
    left: -100%;
    opacity: 0.7;
    animation: scan 3s linear infinite;
    box-shadow: 0 0 10px var(--secondary-color);
  }

  .robot-body {
    width: 120px;
    height: 100px;
    background: linear-gradient(145deg, #a0a0a0, #676767);
    border-radius: 10px;
    position: absolute;
    bottom: -30px;
    left: 15px;
    border: 2px solid var(--primary-color);
  }

  .core {
    width: 30px;
    height: 30px;
    background: var(--secondary-color);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: pulse-core 2s infinite;
    box-shadow: 0 0 20px var(--secondary-color), 0 0 30px var(--secondary-color) inset;
  }

  .loading-text {
    margin-top: 180px;
    font-size: 1.8em;
    letter-spacing: 4px;
    text-shadow: 0 0 5px var(--primary-color), 0 0 10px var(--primary-color);
  }

  .loading-text span {
    animation: flicker 2s infinite;
    opacity: 0;
  }
  .loading-text span:nth-child(1) { animation-delay: 0.2s; }
  .loading-text span:nth-child(2) { animation-delay: 0.4s; }
  .loading-text span:nth-child(3) { animation-delay: 0.6s; }
  .loading-text span:nth-child(4) { animation-delay: 0.8s; }
  .loading-text span:nth-child(5) { animation-delay: 1.0s; }
  .loading-text span:nth-child(6) { animation-delay: 1.2s; }
  .loading-text span:nth-child(7) { animation-delay: 1.4s; }
  .loading-text span:nth-child(8) { animation-delay: 1.6s; }

  @keyframes hover {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-25px); }
  }

  @keyframes scan {
    0% { left: -100%; }
    50%, 100% { left: 100%; }
  }

  @keyframes pulse-core {
    0% { transform: translate(-50%, -50%) scale(0.9); box-shadow: 0 0 20px var(--secondary-color), 0 0 30px var(--secondary-color) inset; }
    70% { transform: translate(-50%, -50%) scale(1.1); box-shadow: 0 0 35px var(--secondary-color), 0 0 45px var(--secondary-color) inset; }
    100% { transform: translate(-50%, -50%) scale(0.9); box-shadow: 0 0 20px var(--secondary-color), 0 0 30px var(--secondary-color) inset; }
  }

  @keyframes flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
  }

  @keyframes pan-grid {
    from { background-position: 0 0; }
    to { background-position: 50px 50px; }
  }

  @keyframes float {
    from { transform: translateY(100vh) scale(0); opacity: 1; }
    to { transform: translateY(-100vh) scale(1); opacity: 0; }
  }
</style>
<style>
  /* 圆形 Logo 及动画 */
  .logo-wrapper{
    position: relative;
    width: 220px;
    height: 220px;
    margin: 0 auto;
  }

  .logo-outer{
    position:absolute;
    width:220px;
    height:220px;
    border-radius:50%;
    border:2px solid var(--primary-color);
    opacity:0.4;
    animation: pulse-ring 4s ease-in-out infinite;
  }

  .logo-middle{
    position:absolute;
    width:200px;
    height:200px;
    top:10px;
    left:10px;
    border-radius:50%;
    border:2px solid var(--primary-color);
    background:rgba(0,255,255,0.05);
  }

  .logo-inner{
    position:absolute;
    width:120px;
    height:120px;
    top:50%;
    left:50%;
    transform:translate(-50%,-50%);
    border-radius:50%;
    background:var(--primary-color);
    display:flex;
    align-items:center;
    justify-content:center;
    color:#0d0d21;
    font-weight:bold;
    font-size:2em;
    box-shadow:0 0 15px var(--primary-color) inset;
  }

  @keyframes pulse-ring{
    0%,100%{transform:scale(1);} 50%{transform:scale(1.08);} }

  /* 标题和副标题 */
  .title{ margin-top:25px; font-size:2.2em; opacity:0; animation: fadeIn 2s forwards 0.5s; }
  .subtitle{ margin-top:10px; font-size:1em; opacity:0; color:var(--secondary-color); animation: fadeIn 2s forwards 1.2s; }
  @keyframes fadeIn{ to{opacity:1;} }

  /* 进度条 */
  .progress-container{ width:300px; height:8px; background:rgba(255,255,255,0.1); border-radius:4px; margin:30px auto 0; overflow:hidden; opacity:0; animation: fadeIn 1s forwards 1.8s; }
  .progress-bar{ width:0%; height:100%; background:var(--primary-color); animation: loadBar 3s forwards 2s; }
  @keyframes loadBar{ to{width:100%;} }

  /* Loading dots */
  .dots{ margin-top:8px; text-align:center; opacity:0; animation: fadeIn 1s forwards 1.8s; }
  .dots span{ display:inline-block; width:6px; height:6px; margin:0 4px; background:var(--primary-color); border-radius:50%; opacity:0.3; animation: blink 1s infinite; }
  .dots span:nth-child(2){animation-delay:0.2s;} .dots span:nth-child(3){animation-delay:0.4s;}
  @keyframes blink{0%,100%{opacity:0.3;}50%{opacity:1;}}
</style>
</head>
<body>

<div class="scene">
  <div class="grid-background"></div>
  <div class="loader-container">
    <div class="logo-wrapper">
      <div class="logo-outer"></div>
      <div class="logo-middle"></div>
      <div class="logo-inner">信</div>
    </div>

    <div class="title">信小财</div>
    <div class="subtitle">智能财税机器人</div>

    <div class="progress-container">
      <div class="progress-bar"></div>
    </div>
    <div class="dots">
      <span></span><span></span><span></span>
    </div>
  </div>
</div>

<script>
  // 动态创建粒子
  const scene = document.querySelector('.scene');
  const numParticles = 50;
  for (let i = 0; i < numParticles; i++) {
    let particle = document.createElement('div');
    particle.className = 'particle';
    let size = Math.random() * 3 + 1;
    particle.style.width = size + 'px';
    particle.style.height = size + 'px';
    particle.style.top = Math.random() * 100 + 'vh';
    particle.style.left = Math.random() * 100 + 'vw';
    particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
    particle.style.animationDelay = Math.random() * -20 + 's';
    scene.appendChild(particle);
  }
</script>

</body>
</html>
